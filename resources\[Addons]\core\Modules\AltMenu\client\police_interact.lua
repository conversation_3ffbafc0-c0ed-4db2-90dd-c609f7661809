-- Menu pour les amendes
local fine_menu = RageUI.AddMenu("", "Amendes")
fine_menu:SetSpriteBanner("commonmenu", "interaction_legal")
fine_menu:SetButtonColor(0, 137, 201, 255)

local InteractMenuPolice = {}

-- Fonction pour vérifier si le joueur est police
local function isPlayerPolice()
    local playerData = ESX.GetPlayerData()
    return playerData.job and playerData.job.name == "police"
end

-- Fonction pour vérifier si le joueur ciblé lève les mains
local function isPlayerHandsUp(targetPed)
    return IsEntityPlayingAnim(targetPed, 'random@mugging3', 'handsup_standing_base', 3)
end

-- Fonction pour vérifier si le joueur ciblé est menotté
local function isPlayerHandcuffed(targetPed)
    return IsEntityPlayingAnim(targetPed, 'mp_arresting', 'idle', 3)
end

-- Menu des amendes
fine_menu:IsVisible(function(Items)
    local closestPlayer, closestDistance = ESX.Game.GetClosestPlayer()

    if Config and Config["PoliceJob"] and Config["PoliceJob"]["Fine"] then
        for k, v in pairs(Config["PoliceJob"]["Fine"]) do
            Items:Button(v.label, nil, {RightLabel = v.price.. "~g~ $"}, true, {
                onSelected = function()
                    if closestPlayer ~= -1 and closestDistance < 5.0 then
                        TriggerServerEvent("iZeyy:police:interact:giveFine", GetPlayerServerId(closestPlayer), v.label)
                        fine_menu:Close()
                    else
                        ESX.ShowNotification("Personne autour de vous")
                    end
                end
            })
        end
    else
        Items:Separator("")
        Items:Separator("Configuration des amendes non trouvée")
        Items:Separator("")
    end
end)

-- Fonction pour charger les interactions police
local function loadPoliceInteractions()
    CreateThread(function()
        while Client.Player == nil do
            Wait(100)
        end

        -- Bouton principal Police
        InteractMenuPolice.main = Game.InteractContext:AddButton("ped_menu", "Police", nil, function(onSelected, Entity)
            if (onSelected) then end
        end, function(Entity)
            -- Vérifier si le joueur est police et que l'entité n'est pas lui-même
            return isPlayerPolice() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID)
        end)

        -- Sous-menu: Menotter
        InteractMenuPolice.handcuff = Game.InteractContext:AddSubMenu(InteractMenuPolice.main, "Menotter", nil, function(onSelected, Entity)
            if (onSelected) then
                local targetPlayer = NetworkGetPlayerIndexFromPed(Entity.ID)
                if targetPlayer ~= -1 then
                    local targetServerId = GetPlayerServerId(targetPlayer)
                    if not isPlayerHandcuffed(Entity.ID) then
                        TriggerServerEvent("iZeyy:police:interact:handcuff", targetServerId)
                    else
                        ESX.ShowNotification("Cette personne est déjà menottée")
                    end
                end
            end
        end, function(Entity)
            return isPlayerPolice() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID) and not isPlayerHandcuffed(Entity.ID)
        end)

        -- Sous-menu: Démenotter
        InteractMenuPolice.unhandcuff = Game.InteractContext:AddSubMenu(InteractMenuPolice.main, "Démenotter", nil, function(onSelected, Entity)
            if (onSelected) then
                local targetPlayer = NetworkGetPlayerIndexFromPed(Entity.ID)
                if targetPlayer ~= -1 then
                    local targetServerId = GetPlayerServerId(targetPlayer)
                    if isPlayerHandcuffed(Entity.ID) then
                        TriggerServerEvent("iZeyy:police:interact:unhandcuff", targetServerId)
                    else
                        ESX.ShowNotification("Cette personne n'est pas menottée")
                    end
                end
            end
        end, function(Entity)
            return isPlayerPolice() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID) and isPlayerHandcuffed(Entity.ID)
        end)

        -- Sous-menu: Fouiller
        InteractMenuPolice.frisk = Game.InteractContext:AddSubMenu(InteractMenuPolice.main, "Fouiller", nil, function(onSelected, Entity)
            if (onSelected) then
                local targetPlayer = NetworkGetPlayerIndexFromPed(Entity.ID)
                if targetPlayer ~= -1 then
                    local targetServerId = GetPlayerServerId(targetPlayer)
                    if isPlayerHandsUp(Entity.ID) then
                        TriggerServerEvent("iZeyy:police:interact:frisk", targetServerId)
                    else
                        ESX.ShowNotification("La personne doit lever les mains")
                    end
                end
            end
        end, function(Entity)
            return isPlayerPolice() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID) and isPlayerHandsUp(Entity.ID)
        end)

        -- Sous-menu: Regarder carte d'identité
        InteractMenuPolice.showId = Game.InteractContext:AddSubMenu(InteractMenuPolice.main, "Carte d'identité", nil, function(onSelected, Entity)
            if (onSelected) then
                local targetPlayer = NetworkGetPlayerIndexFromPed(Entity.ID)
                if targetPlayer ~= -1 then
                    local targetServerId = GetPlayerServerId(targetPlayer)
                    if isPlayerHandsUp(Entity.ID) then
                        TriggerServerEvent("iZeyy:police:interact:showId", targetServerId)
                    else
                        ESX.ShowNotification("La personne doit lever les mains")
                    end
                end
            end
        end, function(Entity)
            return isPlayerPolice() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID) and isPlayerHandsUp(Entity.ID)
        end)

        -- Sous-menu: Voir les factures
        InteractMenuPolice.showBills = Game.InteractContext:AddSubMenu(InteractMenuPolice.main, "Factures", nil, function(onSelected, Entity)
            if (onSelected) then
                local targetPlayer = NetworkGetPlayerIndexFromPed(Entity.ID)
                if targetPlayer ~= -1 then
                    local targetServerId = GetPlayerServerId(targetPlayer)
                    TriggerServerEvent("iZeyy:police:interact:showBills", targetServerId)
                end
            end
        end, function(Entity)
            return isPlayerPolice() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID)
        end)

        -- Sous-menu: Donner une amende
        InteractMenuPolice.giveFine = Game.InteractContext:AddSubMenu(InteractMenuPolice.main, "Amendes", nil, function(onSelected, Entity)
            if (onSelected) then
                fine_menu:Toggle()
            end
        end, function(Entity)
            return isPlayerPolice() and Entity.ID ~= Client.Player:GetPed() and IsPedAPlayer(Entity.ID)
        end)
    end)
end

-- Charger les interactions quand le joueur est connecté
AddEventHandler("fowlmas:onelife:player:receive_player_data", function()
    loadPoliceInteractions()
end)

-- Events pour les animations côté client (réutilisation du code existant)
RegisterNetEvent('iZeyy:police:player:handcuff', function()
    local PlayerPed = PlayerPedId()
    local ControlsAction = {24, 257, 25, 21, 263, 45, 22, 44, 37, 23, 288, 289, 170, 167, 26, 73, 199, 59, 71, 72, 36, 47, 264, 257, 140, 141, 142, 143, 75, 75}

    CreateThread(function()
        local animDict = "mp_arrest_paired"
        RequestAnimDict(animDict)
        while not HasAnimDictLoaded(animDict) do
            Wait(100)
        end
        TaskPlayAnim(PlayerPed, animDict, "crook_p2_back_right", 8.0, -8.0, 5500, 33, 0, false, false, false)
    end)

    isHandcuff = true

    CreateThread(function()
        if (isHandcuff) then
            local animDict = "mp_arresting"
            RequestAnimDict(animDict)
            while not HasAnimDictLoaded(animDict) do
                Wait(100)
            end
            TaskPlayAnim(PlayerPed, animDict, "idle", 1.0, -1.0, -1, 1, 1, false, false, false)
            SetEnableHandcuffs(PlayerPed, true)
            SetPedCanPlayGestureAnims(PlayerPed, false)

            while isHandcuff do
                if not IsEntityPlayingAnim(PlayerPed, animDict, "idle", 3) then
                    TaskPlayAnim(PlayerPed, animDict, "idle", 1.0, -1.0, -1, 1, 1, false, false, false)
                end
                
                for i=1, #ControlsAction do
                    DisableControlAction(0, ControlsAction[i], true)
                end
                Wait(1000)
            end
        else
            ClearPedTasks(PlayerPed)
            SetEnableHandcuffs(PlayerPed, false)
            SetPedCanPlayGestureAnims(PlayerPed, true)
        end
    end)
end)

RegisterNetEvent("iZeyy:police:cop:handcuff", function()
    local PlayerPed = PlayerPedId()

    CreateThread(function()
        local animDict = "mp_arrest_paired"
        RequestAnimDict(animDict)
        while not HasAnimDictLoaded(animDict) do
            Wait(100)
        end
        TaskPlayAnim(PlayerPed, animDict, "cop_p2_back_right", 8.0, -8.0, 5500, 33, 0, false, false, false)
    end)
end)

RegisterNetEvent("iZeyy:police:player:unhandcuff", function()
    local PlayerPed = PlayerPedId()

    isHandcuff = false
    ClearPedTasksImmediately(PlayerPed)
    SetEnableHandcuffs(PlayerPed, false)
    SetPedCanPlayGestureAnims(PlayerPed, true)
    ESX.ShowNotification("Vous avez été démenotté.")
end)
