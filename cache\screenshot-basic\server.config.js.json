[{"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\[webpack]\\webpack\\node_modules\\webpack\\buildin\\module.js", "stats": {"mtime": 1751673877609.9048, "size": 497, "inode": 281474977306871}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@citizenfx\\client\\index.d.ts", "stats": {"mtime": 1751673779652.8596, "size": 3330, "inode": 281474977294979}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@citizenfx\\client\\natives_universal.d.ts", "stats": {"mtime": 1751673779642.3503, "size": 1576346, "inode": 281474977294981}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@citizenfx\\http-wrapper\\index.js", "stats": {"mtime": 1751673779816.9998, "size": 6129, "inode": 281474977294985}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@citizenfx\\server\\index.d.ts", "stats": {"mtime": 1751673807576.3174, "size": 3327, "inode": 281474977294990}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@citizenfx\\server\\natives_server.d.ts", "stats": {"mtime": 1751673807618.854, "size": 68153, "inode": 281474977294992}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\accepts\\index.d.ts", "stats": {"mtime": 1751673760126.6057, "size": 4617, "inode": 281474977295806}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\body-parser\\index.d.ts", "stats": {"mtime": 1751673759355.9453, "size": 4247, "inode": 281474977295811}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\connect\\index.d.ts", "stats": {"mtime": 1751673758680.8662, "size": 3433, "inode": 281474977295816}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\content-disposition\\index.d.ts", "stats": {"mtime": 1751673756456.958, "size": 2082, "inode": 281474977295821}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\cookies\\index.d.ts", "stats": {"mtime": 1751673758520.7283, "size": 5721, "inode": 281474977295826}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\express-serve-static-core\\index.d.ts", "stats": {"mtime": 1751673761421.2168, "size": 37248, "inode": 281474977295836}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\express\\index.d.ts", "stats": {"mtime": 1751673760284.7422, "size": 4734, "inode": 281474977295831}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\formidable\\index.d.ts", "stats": {"mtime": 1751673759511.5786, "size": 1362, "inode": 281474977295841}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\http-assert\\index.d.ts", "stats": {"mtime": 1751673760763.152, "size": 1885, "inode": 281474977295846}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\http-errors\\index.d.ts", "stats": {"mtime": 1751673759666.7112, "size": 3011, "inode": 281474977295851}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\keygrip\\index.d.ts", "stats": {"mtime": 1751673761089.4324, "size": 617, "inode": 281474977295856}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\koa-compose\\index.d.ts", "stats": {"mtime": 1751673759823.346, "size": 2608, "inode": 281474977295866}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\koa-router\\index.d.ts", "stats": {"mtime": 1751673760600.0122, "size": 17132, "inode": 281474977295871}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\koa\\index.d.ts", "stats": {"mtime": 1751673761259.5784, "size": 20105, "inode": 281474977295861}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\mime\\index.d.ts", "stats": {"mtime": 1751673759151.2698, "size": 627, "inode": 281474977295876}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\mv\\index.d.ts", "stats": {"mtime": 1751673758837.501, "size": 538, "inode": 281474977295883}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\async_hooks.d.ts", "stats": {"mtime": 1751673757930.222, "size": 9693, "inode": 281474977295889}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\buffer.d.ts", "stats": {"mtime": 1751673757047.4644, "size": 708, "inode": 281474977295891}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\child_process.d.ts", "stats": {"mtime": 1751673756550.0374, "size": 25084, "inode": 281474977295892}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\cluster.d.ts", "stats": {"mtime": 1751673757629.4639, "size": 16125, "inode": 281474977295893}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\console.d.ts", "stats": {"mtime": 1751673757991.2742, "size": 5923, "inode": 281474977295894}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\constants.d.ts", "stats": {"mtime": 1751673756524.016, "size": 459, "inode": 281474977295895}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\crypto.d.ts", "stats": {"mtime": 1751673757263.6501, "size": 46334, "inode": 281474977295896}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\dgram.d.ts", "stats": {"mtime": 1751673756969.8982, "size": 7195, "inode": 281474977295897}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\dns.d.ts", "stats": {"mtime": 1751673756926.8606, "size": 16315, "inode": 281474977295898}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\domain.d.ts", "stats": {"mtime": 1751673757519.8699, "size": 715, "inode": 281474977295899}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\events.d.ts", "stats": {"mtime": 1751673757864.1653, "size": 3691, "inode": 281474977295900}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\fs.d.ts", "stats": {"mtime": 1751673757858.6602, "size": 117840, "inode": 281474977295901}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\fs\\promises.d.ts", "stats": {"mtime": 1751673758087.3567, "size": 30101, "inode": 281474977295936}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\globals.d.ts", "stats": {"mtime": 1751673757989.2727, "size": 23701, "inode": 281474977295902}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\http.d.ts", "stats": {"mtime": 1751673757921.7146, "size": 18871, "inode": 281474977295904}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\http2.d.ts", "stats": {"mtime": 1751673757771.586, "size": 56595, "inode": 281474977295905}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\https.d.ts", "stats": {"mtime": 1751673756582.0654, "size": 1684, "inode": 281474977295906}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\inspector.d.ts", "stats": {"mtime": 1751673757188.0852, "size": 121933, "inode": 281474977295908}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\module.d.ts", "stats": {"mtime": 1751673757389.7583, "size": 1643, "inode": 281474977295910}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\net.d.ts", "stats": {"mtime": 1751673757433.796, "size": 12897, "inode": 281474977295911}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\os.d.ts", "stats": {"mtime": 1751673756668.6396, "size": 8178, "inode": 281474977295912}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\path.d.ts", "stats": {"mtime": 1751673756773.7295, "size": 6309, "inode": 281474977295914}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\perf_hooks.d.ts", "stats": {"mtime": 1751673757653.4846, "size": 10616, "inode": 281474977295915}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\process.d.ts", "stats": {"mtime": 1751673757497.3506, "size": 20332, "inode": 281474977295916}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\punycode.d.ts", "stats": {"mtime": 1751673756741.702, "size": 3159, "inode": 281474977295917}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\querystring.d.ts", "stats": {"mtime": 1751673757111.5198, "size": 1046, "inode": 281474977295918}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\readline.d.ts", "stats": {"mtime": 1751673756644.1184, "size": 7330, "inode": 281474977295919}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\repl.d.ts", "stats": {"mtime": 1751673757035.954, "size": 18118, "inode": 281474977295921}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\stream.d.ts", "stats": {"mtime": 1751673757461.319, "size": 19374, "inode": 281474977295922}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\string_decoder.d.ts", "stats": {"mtime": 1751673756711.1758, "size": 193, "inode": 281474977295923}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\timers.d.ts", "stats": {"mtime": 1751673756607.5872, "size": 826, "inode": 281474977295924}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\tls.d.ts", "stats": {"mtime": 1751673757698.523, "size": 37760, "inode": 281474977295925}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\trace_events.d.ts", "stats": {"mtime": 1751673756987.9138, "size": 2113, "inode": 281474977295926}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\ts3.4\\assert.d.ts", "stats": {"mtime": 1751673758148.9094, "size": 4648, "inode": 281474977295938}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\ts3.4\\base.d.ts", "stats": {"mtime": 1751673758212.9646, "size": 2420, "inode": 281474977295939}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\ts3.4\\globals.global.d.ts", "stats": {"mtime": 1751673758194.4482, "size": 35, "inode": 281474977295940}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\ts3.4\\index.d.ts", "stats": {"mtime": 1751673758257.5027, "size": 429, "inode": 281474977295941}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\tty.d.ts", "stats": {"mtime": 1751673756801.2534, "size": 2442, "inode": 281474977295927}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\url.d.ts", "stats": {"mtime": 1751673757567.411, "size": 4209, "inode": 281474977295928}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\util.d.ts", "stats": {"mtime": 1751673757311.6914, "size": 12753, "inode": 281474977295929}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\v8.d.ts", "stats": {"mtime": 1751673757727.548, "size": 6769, "inode": 281474977295930}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\vm.d.ts", "stats": {"mtime": 1751673756841.788, "size": 5900, "inode": 281474977295931}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\worker_threads.d.ts", "stats": {"mtime": 1751673756866.8096, "size": 11446, "inode": 281474977295933}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\zlib.d.ts", "stats": {"mtime": 1751673758052.3267, "size": 14327, "inode": 281474977295934}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\qs\\index.d.ts", "stats": {"mtime": 1751673758993.134, "size": 2671, "inode": 281474977295946}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\range-parser\\index.d.ts", "stats": {"mtime": 1751673759974.976, "size": 1224, "inode": 281474977295951}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\serve-static\\index.d.ts", "stats": {"mtime": 1751673760933.7986, "size": 4606, "inode": 281474977295956}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\uuid\\index.d.ts", "stats": {"mtime": 1751673760445.8801, "size": 2867, "inode": 281474977295961}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\accepts\\index.js", "stats": {"mtime": 1751673653984.5383, "size": 5252, "inode": 281474977296223}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\any-promise\\index.js", "stats": {"mtime": 1751673703829.8042, "size": 49, "inode": 281474977296442}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\any-promise\\loader.js", "stats": {"mtime": 1751673703567.5793, "size": 2581, "inode": 281474977296444}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\any-promise\\register.js", "stats": {"mtime": 1751673703699.6929, "size": 2910, "inode": 281474977296450}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\balanced-match\\index.js", "stats": {"mtime": 1751673598208.183, "size": 1174, "inode": 281474977296588}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\brace-expansion\\index.js", "stats": {"mtime": 1751673535251.6675, "size": 4792, "inode": 281474977296688}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\bytes\\index.js", "stats": {"mtime": 1751673700721.6377, "size": 3469, "inode": 281474977296897}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\cache-content-type\\index.js", "stats": {"mtime": 1751673529805.495, "size": 317, "inode": 281474977296942}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\co-body\\index.js", "stats": {"mtime": 1751673778467.3425, "size": 163, "inode": 281474977297164}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\co-body\\lib\\any.js", "stats": {"mtime": 1751673778701.043, "size": 1204, "inode": 281474977297168}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\co-body\\lib\\form.js", "stats": {"mtime": 1751673778598.4548, "size": 1365, "inode": 281474977297169}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\co-body\\lib\\json.js", "stats": {"mtime": 1751673778657.005, "size": 1621, "inode": 281474977297170}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\co-body\\lib\\text.js", "stats": {"mtime": 1751673778574.4343, "size": 1054, "inode": 281474977297171}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\co-body\\lib\\utils.js", "stats": {"mtime": 1751673778637.9888, "size": 189, "inode": 281474977297172}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\co\\index.js", "stats": {"mtime": 1751673652286.5818, "size": 5036, "inode": 281474977297158}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\concat-map\\index.js", "stats": {"mtime": 1751673598532.4614, "size": 345, "inode": 281474977297219}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\content-disposition\\index.js", "stats": {"mtime": 1751673530040.1965, "size": 10594, "inode": 281474977297253}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\content-disposition\\node_modules\\safe-buffer\\index.js", "stats": {"mtime": 1751673530249.8762, "size": 1529, "inode": 281474977297260}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\content-type\\index.js", "stats": {"mtime": 1751673699405.5088, "size": 4809, "inode": 281474977297266}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\cookies\\index.js", "stats": {"mtime": 1751673534860.8323, "size": 6133, "inode": 281474977297272}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\cookies\\node_modules\\depd\\index.js", "stats": {"mtime": 1751673535008.4587, "size": 10932, "inode": 281474977297279}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\deep-equal\\index.js", "stats": {"mtime": 1751673694755.519, "size": 3051, "inode": 281474977297414}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\deep-equal\\lib\\is_arguments.js", "stats": {"mtime": 1751673694814.5698, "size": 641, "inode": 281474977297421}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\deep-equal\\lib\\keys.js", "stats": {"mtime": 1751673694849.5999, "size": 202, "inode": 281474977297422}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\delegates\\index.js", "stats": {"mtime": 1751673532077.4443, "size": 2065, "inode": 281474977297478}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\depd\\index.js", "stats": {"mtime": 1751673610342.5942, "size": 10669, "inode": 281474977297487}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\depd\\lib\\compat\\callsite-tostring.js", "stats": {"mtime": 1751673610493.7236, "size": 2229, "inode": 281474977297495}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\depd\\lib\\compat\\event-listener-count.js", "stats": {"mtime": 1751673610521.2476, "size": 338, "inode": 281474977297496}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\depd\\lib\\compat\\index.js", "stats": {"mtime": 1751673610564.2847, "size": 1421, "inode": 281474977297497}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\destroy\\index.js", "stats": {"mtime": 1751673568582.2646, "size": 1043, "inode": 281474977297518}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\ee-first\\index.js", "stats": {"mtime": 1751673615668.1636, "size": 1684, "inode": 281474977297658}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\encodeurl\\index.js", "stats": {"mtime": 1751673729726.0232, "size": 1586, "inode": 281474977297694}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\escape-html\\index.js", "stats": {"mtime": 1751673735124.655, "size": 1362, "inode": 281474977298691}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\formidable\\lib\\file.js", "stats": {"mtime": 1751673588148.5515, "size": 1643, "inode": 281474977298884}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\formidable\\lib\\incoming_form.js", "stats": {"mtime": 1751673588170.0706, "size": 13698, "inode": 281474977298885}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\formidable\\lib\\index.js", "stats": {"mtime": 1751673588284.1682, "size": 133, "inode": 281474977298886}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\formidable\\lib\\json_parser.js", "stats": {"mtime": 1751673588101.011, "size": 650, "inode": 281474977298887}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\formidable\\lib\\multipart_parser.js", "stats": {"mtime": 1751673588218.1118, "size": 8460, "inode": 281474977298888}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\formidable\\lib\\octet_parser.js", "stats": {"mtime": 1751673588077.9917, "size": 456, "inode": 281474977298889}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\formidable\\lib\\querystring_parser.js", "stats": {"mtime": 1751673588239.1301, "size": 740, "inode": 281474977298890}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\fresh\\index.js", "stats": {"mtime": 1751673764991.7805, "size": 2711, "inode": 281474977298898}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\has-flag\\index.js", "stats": {"mtime": 1751673716463.644, "size": 320, "inode": 281474977298993}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\http-assert\\index.js", "stats": {"mtime": 1751673722299.6516, "size": 937, "inode": 281474977299211}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\http-errors\\index.js", "stats": {"mtime": 1751673589595.2932, "size": 5878, "inode": 281474977299217}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\dbcs-codec.js", "stats": {"mtime": 1751673717102.6921, "size": 21415, "inode": 281474977299232}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\dbcs-data.js", "stats": {"mtime": 1751673717026.1267, "size": 8291, "inode": 281474977299233}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\index.js", "stats": {"mtime": 1751673717179.2583, "size": 710, "inode": 281474977299234}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\internal.js", "stats": {"mtime": 1751673717179.2583, "size": 6115, "inode": 281474977299235}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\sbcs-codec.js", "stats": {"mtime": 1751673717038.638, "size": 2191, "inode": 281474977299236}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\sbcs-data-generated.js", "stats": {"mtime": 1751673716913.0295, "size": 32034, "inode": 281474977299237}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\sbcs-data.js", "stats": {"mtime": 1751673716961.5715, "size": 4686, "inode": 281474977299238}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\tables\\big5-added.json", "stats": {"mtime": 1751673717411.457, "size": 17717, "inode": 281474977299242}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\tables\\cp936.json", "stats": {"mtime": 1751673717280.3452, "size": 47320, "inode": 281474977299243}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\tables\\cp949.json", "stats": {"mtime": 1751673717358.412, "size": 38122, "inode": 281474977299244}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\tables\\cp950.json", "stats": {"mtime": 1751673717348.4036, "size": 42356, "inode": 281474977299245}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\tables\\eucjp.json", "stats": {"mtime": 1751673717487.5225, "size": 41064, "inode": 281474977299246}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\tables\\gb18030-ranges.json", "stats": {"mtime": 1751673717418.9636, "size": 2216, "inode": 281474977299247}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\tables\\gbk-added.json", "stats": {"mtime": 1751673717276.3416, "size": 1227, "inode": 281474977299248}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\tables\\shiftjis.json", "stats": {"mtime": 1751673717499.0327, "size": 23782, "inode": 281474977299249}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\utf16.js", "stats": {"mtime": 1751673716977.585, "size": 5011, "inode": 281474977299239}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\iconv-lite\\encodings\\utf7.js", "stats": {"mtime": 1751673717102.6921, "size": 9215, "inode": 281474977299240}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\iconv-lite\\lib\\bom-handling.js", "stats": {"mtime": 1751673716756.3955, "size": 1109, "inode": 281474977299251}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\iconv-lite\\lib\\extend-node.js", "stats": {"mtime": 1751673716729.3726, "size": 8701, "inode": 281474977299252}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\iconv-lite\\lib\\index.js", "stats": {"mtime": 1751673716874.4966, "size": 5123, "inode": 281474977299254}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\iconv-lite\\lib\\streams.js", "stats": {"mtime": 1751673716820.4502, "size": 3387, "inode": 281474977299255}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\inflation\\index.js", "stats": {"mtime": 1751673635809.9446, "size": 637, "inode": 281474977299284}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\inflight\\inflight.js", "stats": {"mtime": 1751673588773.0881, "size": 1365, "inode": 281474977299289}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\inherits\\inherits.js", "stats": {"mtime": 1751673614222.4243, "size": 250, "inode": 281474977299294}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\inherits\\inherits_browser.js", "stats": {"mtime": 1751673614137.8599, "size": 753, "inode": 281474977299295}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\is-generator-function\\index.js", "stats": {"mtime": 1751673633932.834, "size": 912, "inode": 281474977299396}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\keygrip\\index.js", "stats": {"mtime": 1751673736323.6836, "size": 1158, "inode": 281474977299541}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa-body\\index.d.ts", "stats": {"mtime": 1751673745689.2192, "size": 5400, "inode": 281474977299624}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa-body\\index.js", "stats": {"mtime": 1751673745752.2732, "size": 6211, "inode": 281474977299625}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa-body\\unparsed.js", "stats": {"mtime": 1751673745557.606, "size": 275, "inode": 281474977299629}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa-compose\\index.js", "stats": {"mtime": 1751673628692.8386, "size": 1189, "inode": 281474977299632}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa-convert\\index.js", "stats": {"mtime": 1751673691371.1152, "size": 1584, "inode": 281474977299638}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa-router\\lib\\layer.js", "stats": {"mtime": 1751673719865.563, "size": 5750, "inode": 281474977299649}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa-router\\lib\\router.js", "stats": {"mtime": 1751673719894.0872, "size": 17653, "inode": 281474977299651}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa-router\\node_modules\\debug\\src\\browser.js", "stats": {"mtime": 1751673720730.8054, "size": 6285, "inode": 281474977299660}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa-router\\node_modules\\debug\\src\\common.js", "stats": {"mtime": 1751673720767.3362, "size": 5929, "inode": 281474977299661}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa-router\\node_modules\\debug\\src\\index.js", "stats": {"mtime": 1751673720832.8933, "size": 331, "inode": 281474977299662}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa-router\\node_modules\\debug\\src\\node.js", "stats": {"mtime": 1751673720794.3599, "size": 4415, "inode": 281474977299663}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa-router\\node_modules\\http-errors\\index.js", "stats": {"mtime": 1751673720100.765, "size": 6521, "inode": 281474977299666}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa-router\\node_modules\\ms\\index.js", "stats": {"mtime": 1751673720482.0918, "size": 3024, "inode": 281474977299671}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa-router\\node_modules\\setprototypeof\\index.js", "stats": {"mtime": 1751673720263.9048, "size": 407, "inode": 281474977299677}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa\\lib\\application.js", "stats": {"mtime": 1751673738105.2122, "size": 7342, "inode": 281474977299558}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa\\lib\\context.js", "stats": {"mtime": 1751673737967.5942, "size": 5582, "inode": 281474977299559}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa\\lib\\request.js", "stats": {"mtime": 1751673738037.6543, "size": 14464, "inode": 281474977299560}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa\\lib\\response.js", "stats": {"mtime": 1751673738032.1494, "size": 12101, "inode": 281474977299561}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa\\node_modules\\debug\\src\\browser.js", "stats": {"mtime": 1751673739729.6062, "size": 5707, "inode": 281474977299576}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa\\node_modules\\debug\\src\\debug.js", "stats": {"mtime": 1751673739804.6704, "size": 4889, "inode": 281474977299577}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa\\node_modules\\debug\\src\\index.js", "stats": {"mtime": 1751673739830.7024, "size": 263, "inode": 281474977299578}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa\\node_modules\\debug\\src\\node.js", "stats": {"mtime": 1751673739758.1301, "size": 4339, "inode": 281474977299579}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa\\node_modules\\depd\\index.js", "stats": {"mtime": 1751673738795.3042, "size": 10932, "inode": 281474977299582}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa\\node_modules\\http-errors\\index.js", "stats": {"mtime": 1751673738270.8545, "size": 6521, "inode": 281474977299591}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa\\node_modules\\http-errors\\node_modules\\depd\\index.js", "stats": {"mtime": 1751673738432.9932, "size": 10669, "inode": 281474977299598}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa\\node_modules\\http-errors\\node_modules\\depd\\lib\\compat\\callsite-tostring.js", "stats": {"mtime": 1751673738576.617, "size": 2229, "inode": 281474977299606}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa\\node_modules\\http-errors\\node_modules\\depd\\lib\\compat\\event-listener-count.js", "stats": {"mtime": 1751673738617.1519, "size": 338, "inode": 281474977299607}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa\\node_modules\\http-errors\\node_modules\\depd\\lib\\compat\\index.js", "stats": {"mtime": 1751673738639.671, "size": 1421, "inode": 281474977299608}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa\\node_modules\\koa-compose\\index.js", "stats": {"mtime": 1751673738993.4744, "size": 1115, "inode": 281474977299611}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\koa\\node_modules\\setprototypeof\\index.js", "stats": {"mtime": 1751673739218.1667, "size": 407, "inode": 281474977299616}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\media-typer\\index.js", "stats": {"mtime": 1751673650061.6726, "size": 6375, "inode": 281474977300798}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\methods\\index.js", "stats": {"mtime": 1751673724903.3855, "size": 1040, "inode": 281474977300811}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\mime-db\\db.json", "stats": {"mtime": 1751673567584.9092, "size": 180015, "inode": 281474977300864}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\mime-db\\index.js", "stats": {"mtime": 1751673567646.9624, "size": 136, "inode": 281474977300866}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\mime-types\\index.js", "stats": {"mtime": 1751673652650.3936, "size": 3663, "inode": 281474977300872}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\minimatch\\minimatch.js", "stats": {"mtime": 1751673568827.475, "size": 25347, "inode": 281474977300892}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\mkdirp\\index.js", "stats": {"mtime": 1751673776523.175, "size": 2644, "inode": 281474977300938}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\ms\\index.js", "stats": {"mtime": 1751673692988.5027, "size": 2764, "inode": 281474977300952}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\mv\\index.js", "stats": {"mtime": 1751673566623.0842, "size": 2533, "inode": 281474977300959}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\mv\\node_modules\\glob\\common.js", "stats": {"mtime": 1751673567338.1973, "size": 5584, "inode": 281474977300965}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\mv\\node_modules\\glob\\glob.js", "stats": {"mtime": 1751673567406.756, "size": 18849, "inode": 281474977300966}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\mv\\node_modules\\glob\\sync.js", "stats": {"mtime": 1751673567328.189, "size": 11461, "inode": 281474977300970}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\mv\\node_modules\\rimraf\\rimraf.js", "stats": {"mtime": 1751673567098.9966, "size": 7937, "inode": 281474977300976}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\ncp\\lib\\ncp.js", "stats": {"mtime": 1751673608311.3523, "size": 6112, "inode": 281474977301028}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\negotiator\\index.js", "stats": {"mtime": 1751673626279.7683, "size": 3344, "inode": 281474977301054}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\negotiator\\lib\\charset.js", "stats": {"mtime": 1751673626360.8374, "size": 3081, "inode": 281474977301059}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\negotiator\\lib\\encoding.js", "stats": {"mtime": 1751673626368.8445, "size": 3506, "inode": 281474977301060}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\negotiator\\lib\\language.js", "stats": {"mtime": 1751673626422.8906, "size": 3408, "inode": 281474977301061}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\negotiator\\lib\\mediaType.js", "stats": {"mtime": 1751673626427.3943, "size": 5358, "inode": 281474977301062}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\on-finished\\index.js", "stats": {"mtime": 1751673744173.9192, "size": 3686, "inode": 281474977301354}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\once\\once.js", "stats": {"mtime": 1751673605250.726, "size": 935, "inode": 281474977301360}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\only\\index.js", "stats": {"mtime": 1751673592462.2534, "size": 247, "inode": 281474977301366}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\parseurl\\index.js", "stats": {"mtime": 1751673597229.844, "size": 2809, "inode": 281474977301448}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\path-is-absolute\\index.js", "stats": {"mtime": 1751673691510.735, "size": 611, "inode": 281474977301475}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\path-to-regexp\\index.js", "stats": {"mtime": 1751673737316.035, "size": 10854, "inode": 281474977301482}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\path-to-regexp\\node_modules\\isarray\\index.js", "stats": {"mtime": 1751673737485.681, "size": 120, "inode": 281474977301489}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\qs\\lib\\formats.js", "stats": {"mtime": 1751673573085.1277, "size": 521, "inode": 281474977301659}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\qs\\lib\\index.js", "stats": {"mtime": 1751673573148.6824, "size": 211, "inode": 281474977301660}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\qs\\lib\\parse.js", "stats": {"mtime": 1751673573077.1213, "size": 9197, "inode": 281474977301661}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\qs\\lib\\stringify.js", "stats": {"mtime": 1751673573016.5686, "size": 8030, "inode": 281474977301662}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\qs\\lib\\utils.js", "stats": {"mtime": 1751673573142.177, "size": 6636, "inode": 281474977301663}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\raw-body\\index.js", "stats": {"mtime": 1751673809101.6262, "size": 6059, "inode": 281474977301721}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\safer-buffer\\safer.js", "stats": {"mtime": 1751673533532.6929, "size": 2110, "inode": 281474977301962}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\setprototypeof\\index.js", "stats": {"mtime": 1751673692757.3047, "size": 384, "inode": 281474977302000}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\statuses\\codes.json", "stats": {"mtime": 1751673725315.239, "size": 1821, "inode": 281474977302172}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\statuses\\index.js", "stats": {"mtime": 1751673725321.7444, "size": 2088, "inode": 281474977302174}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\supports-color\\index.js", "stats": {"mtime": 1751673725061.5212, "size": 2771, "inode": 281474977302308}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\toidentifier\\index.js", "stats": {"mtime": 1751673764801.1172, "size": 490, "inode": 281474977302438}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\tsscmp\\lib\\index.js", "stats": {"mtime": 1751673649603.2795, "size": 1176, "inode": 281474977302511}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\type-is\\index.js", "stats": {"mtime": 1751673534182.7507, "size": 5562, "inode": 281474977302524}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.collection.d.ts", "stats": {"mtime": 1751673621635.7832, "size": 2845, "inode": 281474977302558}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.core.d.ts", "stats": {"mtime": 1751673620988.7283, "size": 19608, "inode": 281474977302559}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.d.ts", "stats": {"mtime": 1751673617568.2935, "size": 1250, "inode": 281474977302560}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.generator.d.ts", "stats": {"mtime": 1751673620052.4248, "size": 2129, "inode": 281474977302561}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.iterable.d.ts", "stats": {"mtime": 1751673620439.7576, "size": 14536, "inode": 281474977302562}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.promise.d.ts", "stats": {"mtime": 1751673617527.2588, "size": 10652, "inode": 281474977302563}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.proxy.d.ts", "stats": {"mtime": 1751673617494.2307, "size": 1961, "inode": 281474977302564}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.reflect.d.ts", "stats": {"mtime": 1751673623888.2166, "size": 1913, "inode": 281474977302565}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.symbol.d.ts", "stats": {"mtime": 1751673620290.129, "size": 1657, "inode": 281474977302566}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.symbol.wellknown.d.ts", "stats": {"mtime": 1751673617358.113, "size": 10257, "inode": 281474977302567}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2016.array.include.d.ts", "stats": {"mtime": 1751673624167.4558, "size": 4870, "inode": 281474977302568}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2016.d.ts", "stats": {"mtime": 1751673621205.9148, "size": 937, "inode": 281474977302569}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2017.d.ts", "stats": {"mtime": 1751673617455.197, "size": 1092, "inode": 281474977302571}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2017.intl.d.ts", "stats": {"mtime": 1751673623820.658, "size": 1253, "inode": 281474977302573}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2017.object.d.ts", "stats": {"mtime": 1751673617390.1406, "size": 2461, "inode": 281474977302574}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2017.sharedmemory.d.ts", "stats": {"mtime": 1751673619679.6055, "size": 6148, "inode": 281474977302575}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2017.string.d.ts", "stats": {"mtime": 1751673619604.0405, "size": 2387, "inode": 281474977302576}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2017.typedarrays.d.ts", "stats": {"mtime": 1751673621648.7947, "size": 1434, "inode": 281474977302577}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2018.d.ts", "stats": {"mtime": 1751673620584.381, "size": 1006, "inode": 281474977302578}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2018.intl.d.ts", "stats": {"mtime": 1751673620362.1912, "size": 1821, "inode": 281474977302580}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2018.promise.d.ts", "stats": {"mtime": 1751673621523.187, "size": 1361, "inode": 281474977302581}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2018.regexp.d.ts", "stats": {"mtime": 1751673621131.8513, "size": 1236, "inode": 281474977302582}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es5.d.ts", "stats": {"mtime": 1751673620710.99, "size": 200360, "inode": 281474977302583}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.esnext.asynciterable.d.ts", "stats": {"mtime": 1751673620918.668, "size": 1544, "inode": 281474977302586}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.esnext.bigint.d.ts", "stats": {"mtime": 1751673621055.786, "size": 30733, "inode": 281474977302587}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.esnext.intl.d.ts", "stats": {"mtime": 1751673621390.573, "size": 1255, "inode": 281474977302590}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\unpipe\\index.js", "stats": {"mtime": 1751673725541.433, "size": 1118, "inode": 281474977302702}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\urijs\\src\\IPv6.js", "stats": {"mtime": 1751673604896.4211, "size": 4422, "inode": 281474977302806}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\urijs\\src\\SecondLevelDomains.js", "stats": {"mtime": 1751673605024.031, "size": 12088, "inode": 281474977302810}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\urijs\\src\\URI.js", "stats": {"mtime": 1751673605095.0918, "size": 66382, "inode": 281474977302813}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\urijs\\src\\punycode.js", "stats": {"mtime": 1751673604953.9707, "size": 14670, "inode": 281474977302809}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\uuid\\index.js", "stats": {"mtime": 1751673706637.2131, "size": 120, "inode": 281474977302900}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\uuid\\lib\\bytesToUuid.js", "stats": {"mtime": 1751673706720.2842, "size": 775, "inode": 281474977302911}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\uuid\\lib\\rng.js", "stats": {"mtime": 1751673706857.9028, "size": 246, "inode": 281474977302915}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\uuid\\v1.js", "stats": {"mtime": 1751673706629.2063, "size": 3331, "inode": 281474977302904}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\uuid\\v4.js", "stats": {"mtime": 1751673706430.536, "size": 680, "inode": 281474977302906}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\vary\\index.js", "stats": {"mtime": 1751673597437.0217, "size": 2930, "inode": 281474977302921}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\wrappy\\wrappy.js", "stats": {"mtime": 1751673765237.992, "size": 905, "inode": 281474977303418}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\ylru\\index.js", "stats": {"mtime": 1751673744668.844, "size": 2337, "inode": 281474977303441}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\src\\server\\server.ts", "stats": {"mtime": 1751673809786.7134, "size": 2523, "inode": 281474977303450}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\src\\server\\types\\@citizenfx\\http-wrapper.d.ts", "stats": {"mtime": 1751673809877.7915, "size": 60, "inode": 281474977303454}}]