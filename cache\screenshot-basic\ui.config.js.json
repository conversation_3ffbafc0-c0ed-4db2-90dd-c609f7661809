[{"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\[webpack]\\webpack\\node_modules\\webpack\\buildin\\global.js", "stats": {"mtime": 1751673877759.033, "size": 472, "inode": 281474977306869}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\[webpack]\\webpack\\node_modules\\webpack\\buildin\\module.js", "stats": {"mtime": 1751673877609.9048, "size": 497, "inode": 281474977306871}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\lodash\\lodash.js", "stats": {"mtime": 1751673660980.5403, "size": 542563, "inode": 281474977299877}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\ui\\index.html", "stats": {"mtime": 1751673809174.1885, "size": 256, "inode": 281474977303456}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\[webpack]\\webpack\\node_modules\\webpack\\buildin\\global.js", "stats": {"mtime": 1751673877759.033, "size": 472, "inode": 281474977306869}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\[webpack]\\webpack\\node_modules\\webpack\\buildin\\module.js", "stats": {"mtime": 1751673877609.9048, "size": 497, "inode": 281474977306871}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@citizenfx\\three\\build\\three.module.js", "stats": {"mtime": 1751673780273.892, "size": 1128130, "inode": 281474977295002}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\accepts\\index.d.ts", "stats": {"mtime": 1751673760126.6057, "size": 4617, "inode": 281474977295806}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\body-parser\\index.d.ts", "stats": {"mtime": 1751673759355.9453, "size": 4247, "inode": 281474977295811}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\connect\\index.d.ts", "stats": {"mtime": 1751673758680.8662, "size": 3433, "inode": 281474977295816}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\content-disposition\\index.d.ts", "stats": {"mtime": 1751673756456.958, "size": 2082, "inode": 281474977295821}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\cookies\\index.d.ts", "stats": {"mtime": 1751673758520.7283, "size": 5721, "inode": 281474977295826}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\express-serve-static-core\\index.d.ts", "stats": {"mtime": 1751673761421.2168, "size": 37248, "inode": 281474977295836}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\express\\index.d.ts", "stats": {"mtime": 1751673760284.7422, "size": 4734, "inode": 281474977295831}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\formidable\\index.d.ts", "stats": {"mtime": 1751673759511.5786, "size": 1362, "inode": 281474977295841}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\http-assert\\index.d.ts", "stats": {"mtime": 1751673760763.152, "size": 1885, "inode": 281474977295846}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\http-errors\\index.d.ts", "stats": {"mtime": 1751673759666.7112, "size": 3011, "inode": 281474977295851}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\keygrip\\index.d.ts", "stats": {"mtime": 1751673761089.4324, "size": 617, "inode": 281474977295856}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\koa-compose\\index.d.ts", "stats": {"mtime": 1751673759823.346, "size": 2608, "inode": 281474977295866}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\koa-router\\index.d.ts", "stats": {"mtime": 1751673760600.0122, "size": 17132, "inode": 281474977295871}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\koa\\index.d.ts", "stats": {"mtime": 1751673761259.5784, "size": 20105, "inode": 281474977295861}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\mime\\index.d.ts", "stats": {"mtime": 1751673759151.2698, "size": 627, "inode": 281474977295876}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\mv\\index.d.ts", "stats": {"mtime": 1751673758837.501, "size": 538, "inode": 281474977295883}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\async_hooks.d.ts", "stats": {"mtime": 1751673757930.222, "size": 9693, "inode": 281474977295889}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\buffer.d.ts", "stats": {"mtime": 1751673757047.4644, "size": 708, "inode": 281474977295891}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\child_process.d.ts", "stats": {"mtime": 1751673756550.0374, "size": 25084, "inode": 281474977295892}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\cluster.d.ts", "stats": {"mtime": 1751673757629.4639, "size": 16125, "inode": 281474977295893}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\console.d.ts", "stats": {"mtime": 1751673757991.2742, "size": 5923, "inode": 281474977295894}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\constants.d.ts", "stats": {"mtime": 1751673756524.016, "size": 459, "inode": 281474977295895}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\crypto.d.ts", "stats": {"mtime": 1751673757263.6501, "size": 46334, "inode": 281474977295896}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\dgram.d.ts", "stats": {"mtime": 1751673756969.8982, "size": 7195, "inode": 281474977295897}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\dns.d.ts", "stats": {"mtime": 1751673756926.8606, "size": 16315, "inode": 281474977295898}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\domain.d.ts", "stats": {"mtime": 1751673757519.8699, "size": 715, "inode": 281474977295899}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\events.d.ts", "stats": {"mtime": 1751673757864.1653, "size": 3691, "inode": 281474977295900}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\fs.d.ts", "stats": {"mtime": 1751673757858.6602, "size": 117840, "inode": 281474977295901}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\fs\\promises.d.ts", "stats": {"mtime": 1751673758087.3567, "size": 30101, "inode": 281474977295936}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\globals.d.ts", "stats": {"mtime": 1751673757989.2727, "size": 23701, "inode": 281474977295902}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\http.d.ts", "stats": {"mtime": 1751673757921.7146, "size": 18871, "inode": 281474977295904}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\http2.d.ts", "stats": {"mtime": 1751673757771.586, "size": 56595, "inode": 281474977295905}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\https.d.ts", "stats": {"mtime": 1751673756582.0654, "size": 1684, "inode": 281474977295906}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\inspector.d.ts", "stats": {"mtime": 1751673757188.0852, "size": 121933, "inode": 281474977295908}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\module.d.ts", "stats": {"mtime": 1751673757389.7583, "size": 1643, "inode": 281474977295910}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\net.d.ts", "stats": {"mtime": 1751673757433.796, "size": 12897, "inode": 281474977295911}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\os.d.ts", "stats": {"mtime": 1751673756668.6396, "size": 8178, "inode": 281474977295912}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\path.d.ts", "stats": {"mtime": 1751673756773.7295, "size": 6309, "inode": 281474977295914}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\perf_hooks.d.ts", "stats": {"mtime": 1751673757653.4846, "size": 10616, "inode": 281474977295915}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\process.d.ts", "stats": {"mtime": 1751673757497.3506, "size": 20332, "inode": 281474977295916}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\punycode.d.ts", "stats": {"mtime": 1751673756741.702, "size": 3159, "inode": 281474977295917}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\querystring.d.ts", "stats": {"mtime": 1751673757111.5198, "size": 1046, "inode": 281474977295918}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\readline.d.ts", "stats": {"mtime": 1751673756644.1184, "size": 7330, "inode": 281474977295919}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\repl.d.ts", "stats": {"mtime": 1751673757035.954, "size": 18118, "inode": 281474977295921}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\stream.d.ts", "stats": {"mtime": 1751673757461.319, "size": 19374, "inode": 281474977295922}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\string_decoder.d.ts", "stats": {"mtime": 1751673756711.1758, "size": 193, "inode": 281474977295923}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\timers.d.ts", "stats": {"mtime": 1751673756607.5872, "size": 826, "inode": 281474977295924}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\tls.d.ts", "stats": {"mtime": 1751673757698.523, "size": 37760, "inode": 281474977295925}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\trace_events.d.ts", "stats": {"mtime": 1751673756987.9138, "size": 2113, "inode": 281474977295926}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\ts3.4\\assert.d.ts", "stats": {"mtime": 1751673758148.9094, "size": 4648, "inode": 281474977295938}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\ts3.4\\base.d.ts", "stats": {"mtime": 1751673758212.9646, "size": 2420, "inode": 281474977295939}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\ts3.4\\globals.global.d.ts", "stats": {"mtime": 1751673758194.4482, "size": 35, "inode": 281474977295940}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\ts3.4\\index.d.ts", "stats": {"mtime": 1751673758257.5027, "size": 429, "inode": 281474977295941}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\tty.d.ts", "stats": {"mtime": 1751673756801.2534, "size": 2442, "inode": 281474977295927}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\url.d.ts", "stats": {"mtime": 1751673757567.411, "size": 4209, "inode": 281474977295928}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\util.d.ts", "stats": {"mtime": 1751673757311.6914, "size": 12753, "inode": 281474977295929}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\v8.d.ts", "stats": {"mtime": 1751673757727.548, "size": 6769, "inode": 281474977295930}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\vm.d.ts", "stats": {"mtime": 1751673756841.788, "size": 5900, "inode": 281474977295931}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\worker_threads.d.ts", "stats": {"mtime": 1751673756866.8096, "size": 11446, "inode": 281474977295933}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\node\\zlib.d.ts", "stats": {"mtime": 1751673758052.3267, "size": 14327, "inode": 281474977295934}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\qs\\index.d.ts", "stats": {"mtime": 1751673758993.134, "size": 2671, "inode": 281474977295946}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\range-parser\\index.d.ts", "stats": {"mtime": 1751673759974.976, "size": 1224, "inode": 281474977295951}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\serve-static\\index.d.ts", "stats": {"mtime": 1751673760933.7986, "size": 4606, "inode": 281474977295956}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\@types\\uuid\\index.d.ts", "stats": {"mtime": 1751673760445.8801, "size": 2867, "inode": 281474977295961}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\lodash\\lodash.js", "stats": {"mtime": 1751673660980.5403, "size": 542563, "inode": 281474977299877}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.dom.d.ts", "stats": {"mtime": 1751673621570.728, "size": 702969, "inode": 281474977302556}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.collection.d.ts", "stats": {"mtime": 1751673621635.7832, "size": 2845, "inode": 281474977302558}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.core.d.ts", "stats": {"mtime": 1751673620988.7283, "size": 19608, "inode": 281474977302559}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.d.ts", "stats": {"mtime": 1751673617568.2935, "size": 1250, "inode": 281474977302560}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.generator.d.ts", "stats": {"mtime": 1751673620052.4248, "size": 2129, "inode": 281474977302561}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.iterable.d.ts", "stats": {"mtime": 1751673620439.7576, "size": 14536, "inode": 281474977302562}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.promise.d.ts", "stats": {"mtime": 1751673617527.2588, "size": 10652, "inode": 281474977302563}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.proxy.d.ts", "stats": {"mtime": 1751673617494.2307, "size": 1961, "inode": 281474977302564}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.reflect.d.ts", "stats": {"mtime": 1751673623888.2166, "size": 1913, "inode": 281474977302565}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.symbol.d.ts", "stats": {"mtime": 1751673620290.129, "size": 1657, "inode": 281474977302566}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2015.symbol.wellknown.d.ts", "stats": {"mtime": 1751673617358.113, "size": 10257, "inode": 281474977302567}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2016.array.include.d.ts", "stats": {"mtime": 1751673624167.4558, "size": 4870, "inode": 281474977302568}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2016.d.ts", "stats": {"mtime": 1751673621205.9148, "size": 937, "inode": 281474977302569}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2017.d.ts", "stats": {"mtime": 1751673617455.197, "size": 1092, "inode": 281474977302571}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2017.intl.d.ts", "stats": {"mtime": 1751673623820.658, "size": 1253, "inode": 281474977302573}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2017.object.d.ts", "stats": {"mtime": 1751673617390.1406, "size": 2461, "inode": 281474977302574}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2017.sharedmemory.d.ts", "stats": {"mtime": 1751673619679.6055, "size": 6148, "inode": 281474977302575}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2017.string.d.ts", "stats": {"mtime": 1751673619604.0405, "size": 2387, "inode": 281474977302576}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2017.typedarrays.d.ts", "stats": {"mtime": 1751673621648.7947, "size": 1434, "inode": 281474977302577}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2018.d.ts", "stats": {"mtime": 1751673620584.381, "size": 1006, "inode": 281474977302578}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2018.intl.d.ts", "stats": {"mtime": 1751673620362.1912, "size": 1821, "inode": 281474977302580}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2018.promise.d.ts", "stats": {"mtime": 1751673621523.187, "size": 1361, "inode": 281474977302581}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es2018.regexp.d.ts", "stats": {"mtime": 1751673621131.8513, "size": 1236, "inode": 281474977302582}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.es5.d.ts", "stats": {"mtime": 1751673620710.99, "size": 200360, "inode": 281474977302583}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.esnext.asynciterable.d.ts", "stats": {"mtime": 1751673620918.668, "size": 1544, "inode": 281474977302586}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.esnext.bigint.d.ts", "stats": {"mtime": 1751673621055.786, "size": 30733, "inode": 281474977302587}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\node_modules\\typescript\\lib\\lib.esnext.intl.d.ts", "stats": {"mtime": 1751673621390.573, "size": 1255, "inode": 281474977302590}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\ui\\index.html", "stats": {"mtime": 1751673809174.1885, "size": 256, "inode": 281474977303456}}, {"name": "B:\\Imperial Clean - Copie - Copie\\resources\\[Frw]\\screenshot-basic\\ui\\src\\main.ts", "stats": {"mtime": 1751673809270.2708, "size": 6636, "inode": 281474977303459}}]