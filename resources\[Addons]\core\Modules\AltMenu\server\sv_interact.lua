AddEventHandler('esx:playerLoaded', function(source)
    local src = source
    local xPlayer = ESX.GetPlayerFromId(src)

    if (xPlayer) then
      TriggerClientEvent("iZeyy:interactMenu:receiveData", source, xPlayer.getFirstName(), xPlayer.getLastName())
    end
end)

-- Events pour les interactions police
RegisterNetEvent("iZeyy:police:interact:handcuff", function(target)
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = ESX.GetPlayerFromId(target)

    if xPlayer and xTarget then
        if xPlayer.job.name == "police" then
            if (target ~= -1 and xTarget) then
                if #(GetEntityCoords(GetPlayerPed(source)) - GetEntityCoords(GetPlayerPed(target))) < 10.0 then
                    TriggerClientEvent("iZeyy:police:player:handcuff", target)
                    TriggerClientEvent("iZeyy:police:cop:handcuff", source)
                    xPlayer.showNotification("Vous avez menotté le joueur.")
                    xTarget.showNotification("Vous avez été menotté par la police.")
                end
            end
        else
            xPlayer.ban(0, "(iZeyy:police:interact:handcuff)")
        end
    end
end)

RegisterNetEvent("iZeyy:police:interact:unhandcuff", function(target)
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = ESX.GetPlayerFromId(target)

    if xPlayer and xTarget then
        if xPlayer.job.name == "police" then
            if target ~= -1 and xTarget then
                local policeCoords = GetEntityCoords(GetPlayerPed(source))
                local targetCoords = GetEntityCoords(GetPlayerPed(target))

                if #(policeCoords - targetCoords) < 10.0 then
                    TriggerClientEvent("iZeyy:police:player:unhandcuff", target)
                    xPlayer.showNotification("Vous avez démenotté le joueur.")
                    xTarget.showNotification("Vous avez été démenotté.")
                else
                    xPlayer.showNotification("Aucun joueur n'est à proximité")
                end
            end
        else
            xPlayer.ban(0, "(iZeyy:police:interact:unhandcuff)")
        end
    end
end)

RegisterNetEvent("iZeyy:police:interact:escort", function(target)
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = ESX.GetPlayerFromId(target)

    if xPlayer and xTarget then
        if xPlayer.job.name == "police" then
            if (target ~= -1 and xTarget) then
                if #(GetEntityCoords(GetPlayerPed(source)) - GetEntityCoords(GetPlayerPed(target))) < 10.0 then
                    TriggerClientEvent("iZeyy:police:player:escort", target, source)
                    xPlayer.showNotification("Vous escortez le joueur.")
                end
            end
        else
            xPlayer.ban(0, "(iZeyy:police:interact:escort)")
        end
    end
end)

RegisterNetEvent("iZeyy:police:interact:frisk", function(target)
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = ESX.GetPlayerFromId(target)

    if (type(xPlayer) == "table" and type(xTarget) == "table") then
        if (xPlayer.job.name ~= "police") then
            return xPlayer.ban(0, "(iZeyy:police:interact:frisk)")
        end

        local player = GetPlayerPed(xPlayer.source)
        local targetPed = GetPlayerPed(xTarget.source)
        local playerCoords = GetEntityCoords(player)
        local targetCoords = GetEntityCoords(targetPed)

        if #(playerCoords - targetCoords) > 15 then
            return xPlayer.ban(0, "(iZeyy:police:interact:frisk) distance")
        end

        exports["inventory"]:FriskTarget(xPlayer.source, xTarget.source)
        xPlayer.showNotification("Vous fouillez le joueur.")
        xTarget.showNotification("Vous êtes en cours de fouille.")
    end
end)

RegisterNetEvent("iZeyy:police:interact:showId", function(target)
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = ESX.GetPlayerFromId(target)

    if (xPlayer and xTarget) then
        if (xPlayer.job.name ~= "police") then
            return xPlayer.ban(0, "(iZeyy:police:interact:showId)")
        end

        local PlayerPed = GetPlayerPed(xPlayer.source)
        local TargetPed = GetPlayerPed(xTarget.source)
        local PlayerCoords = GetEntityCoords(PlayerPed)
        local TargetCoords = GetEntityCoords(TargetPed)

        if #(PlayerCoords - TargetCoords) < 10 then
            TriggerEvent('jsfour-idcard:open', xTarget.source, xPlayer.source)
            xPlayer.showNotification("Vous regardez la carte d'identité du joueur.")
        else
            xPlayer.showNotification("Aucun joueur à côté de vous.")
        end
    end
end)

RegisterNetEvent("iZeyy:police:interact:showBills", function(target)
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = ESX.GetPlayerFromId(target)

    if (xPlayer and xTarget) then
        if (xPlayer.job.name ~= "police") then
            return xPlayer.ban(0, "(iZeyy:police:interact:showBills)")
        end

        local PlayerPed = GetPlayerPed(xPlayer.source)
        local TargetPed = GetPlayerPed(xTarget.source)
        local PlayerCoords = GetEntityCoords(PlayerPed)
        local TargetCoords = GetEntityCoords(TargetPed)

        if #(PlayerCoords - TargetCoords) < 10 then
            -- Récupérer les factures du joueur ciblé
            ESX.TriggerServerCallback('esx_billing:getBills', function(bills)
                if #bills > 0 then
                    local billsText = "Factures du joueur:\n"
                    for i = 1, #bills do
                        billsText = billsText .. "- " .. bills[i].label .. ": " .. bills[i].amount .. "$\n"
                    end
                    xPlayer.showNotification(billsText)
                else
                    xPlayer.showNotification("Le joueur n'a aucune facture.")
                end
            end, xTarget.identifier)
        else
            xPlayer.showNotification("Aucun joueur à côté de vous.")
        end
    end
end)

RegisterNetEvent("iZeyy:police:interact:giveFine", function(target, fineType)
    local xPlayer = ESX.GetPlayerFromId(source)
    local xTarget = ESX.GetPlayerFromId(target)
    local price = 0

    if (not xPlayer) then return; end
    if (not xTarget) then return; end

    if xPlayer.job.name ~= "police" then
        xPlayer.ban(0, "(iZeyy:police:interact:giveFine)")
        return
    end

    local society = ESX.DoesSocietyExist("police")
    if (not society) then return; end

    local PlayerPed = GetPlayerPed(xPlayer.source)
    local TargetPed = GetPlayerPed(xTarget.source)
    local PlayerCoords = GetEntityCoords(PlayerPed)
    local TargetCoords = GetEntityCoords(TargetPed)

    if #(PlayerCoords - TargetCoords) < 10.0 then
        for k, v in pairs(Config["PoliceJob"]["Fine"]) do
            if v.label == fineType then
                price = v.price
                break
            end
        end

        xTarget.removeAccountMoney("bank", price)
        ESX.AddSocietyMoney("police", price / 2)
        ESX.AddSocietyMoney("gouv", price / 2)
        xTarget.showNotification("Votre compte en banque a été réduit de "..price.."~g~$~s~.")
        xPlayer.showNotification("Vous avez donné une amende de "..price.."~g~$~s~")
        CoreSendLogs("Bill", "OneLife | Bill", xPlayer.getLastName().." "..xPlayer.getFirstName().." (***"..xPlayer.identifier.."***) vient d'envoyer une amende de **"..price.."** $ à "..xTarget.getLastName().." "..xTarget.getFirstName().." (***"..xTarget.identifier.."***)", Config["Log"]["Job"]["police"]["send_bill"])
    else
        xPlayer.showNotification("Le joueur est trop loin.")
    end
end)

Shared.Events:OnNet("iZeyy:AltMenu:Recrute", function(xPlayer, Target, Job, Level)
    if type(xPlayer) == "table" then
        local xTarget = ESX.GetPlayerFromId(Target)
        
        if xTarget then
            xTarget.setJob(Job, Level)

            local updatedTargetXPlayer = ESX.GetPlayerFromId(Target)

            local targetName = updatedTargetXPlayer.name
            local targetId = updatedTargetXPlayer.identifier
            local newJobName = updatedTargetXPlayer.job.name
            local newJobGradeName = updatedTargetXPlayer.job.grade_name
            local sourceName = xPlayer.name
            local sourceId = xPlayer.identifier

            CoreSendLogs(
                "Logs Recrutement",
                "OneLife | Recrutement de joueur",
                ("**Joueur recruté :** %s [%s] [%s]\n" ..
                    "**Nouveau Job :** %s - %s\n" ..
                    "**Auteur du recrutement :** %s [%s]"):
                    format(
                        targetName,
                        Target,
                        targetId,
                        newJobName,
                        newJobGradeName,
                        sourceName,
                        sourceId
                    ),
                Config["Log"]["Other"]["InteractMenu_Society"]
            )
            TriggerClientEvent('esx:showNotification', xPlayer.source, "Vous avez recruté " .. targetName )
            TriggerClientEvent('esx:showNotification', xTarget.source, "Vous avez etait recruté par " .. sourceName)
        else
            TriggerClientEvent('esx:showNotification', xPlayer.source, 'Le joueur ciblé est introuvable.')
        end
    end
end)

Shared.Events:OnNet("iZeyy:AltMenu:Byeee", function(xPlayer, Target)
    if type(xPlayer) == "table" then
        local xTarget = ESX.GetPlayerFromId(Target)

        if xTarget then
            if xPlayer.job.grade_name == "boss" and xPlayer.job.name == xTarget.job.name then
                xTarget.setJob("unemployed", 0)

                local updatedTargetXPlayer = ESX.GetPlayerFromId(Target)

                local targetName = updatedTargetXPlayer.name
                local targetId = updatedTargetXPlayer.identifier
                local newJobName = updatedTargetXPlayer.job.name
                local newJobGradeName = updatedTargetXPlayer.job.grade_name
                local sourceName = xPlayer.name
                local sourceId = xPlayer.identifier

                CoreSendLogs(
                    "Logs Licenciement",
                    "OneLife | Licenciement de joueur",
                    ("**Joueur licencié :** %s [%s] [%s]\n" ..
                        "**Nouveau Job :** %s - %s\n" ..
                        "**Auteur du licenciement :** %s [%s]"):
                        format(
                            targetName,
                            Target,
                            targetId,
                            newJobName,
                            newJobGradeName,
                            sourceName,
                            sourceId
                        ),
                    Config["Log"]["Other"]["InteractMenu_Society"]
                )

                TriggerClientEvent('esx:showNotification', xPlayer.source, 'Vous avez ~s~viré ' .. targetName .. '.')
                TriggerClientEvent('esx:showNotification', Target, 'Vous avez été ~s~viré par ' .. sourceName .. '.')
            else
                TriggerClientEvent('esx:showNotification', xPlayer.source, 'Vous n\'avez pas ~s~l\'autorisation.')
            end
        else
            TriggerClientEvent('esx:showNotification', xPlayer.source, 'Le joueur ciblé est introuvable.')
        end
    end
end)